import React, { useState, useCallback } from 'react';
import { pdfjs } from 'react-pdf';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import { ThemeProvider, CssBaseline, Box, Snackbar, Alert } from '@mui/material';
import theme from './theme';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import PDFViewer from './components/PDFViewer';

// Configure PDF.js worker - use webpack import for better compatibility
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

function App() {
  // PDF State
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [fileName, setFileName] = useState('');

  // UI State
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [zoom, setZoom] = useState(100);
  const [selectedTool, setSelectedTool] = useState('text');

  // Text Tool State
  const [text, setText] = useState('');
  const [textColor, setTextColor] = useState({ hex: '#000000' });
  const [fontSize, setFontSize] = useState(24);
  const [fontFamily, setFontFamily] = useState('Helvetica');
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);

  // Text Elements State (for editing and repositioning)
  const [textElements, setTextElements] = useState([]);
  const [selectedTextElement, setSelectedTextElement] = useState(null);
  const [isEditingText, setIsEditingText] = useState(false);
  const [previewText, setPreviewText] = useState(null); // For showing text preview on hover

  // History State
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Notification State
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  // Utility Functions
  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const addToHistory = useCallback((newFile) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newFile);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  // File Handlers
  const onFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setPdfFile(file);
      setFileName(file.name);
      setCurrentPage(1);
      setHistory([file]);
      setHistoryIndex(0);
      showNotification('PDF carregado com sucesso!', 'success');
    }
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  // Zoom Controls
  const handleZoomIn = () => {
    setZoom(prev => Math.min(200, prev + 25));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(50, prev - 25));
  };

  const handleFitToScreen = () => {
    setZoom(100);
  };

  // History Controls
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setPdfFile(history[historyIndex - 1]);
      showNotification('Ação desfeita', 'info');
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setPdfFile(history[historyIndex + 1]);
      showNotification('Ação refeita', 'info');
    }
  };

  // PDF Editing Functions
  const addTextToPdf = useCallback(async (pageNumber, x, y) => {
    if (!pdfFile) {
      showNotification('Nenhum PDF carregado', 'warning');
      return;
    }

    // Use default text if no text is provided
    const textToAdd = text.trim() || 'Texto de exemplo';
    const currentPageBeforeEdit = currentPage; // Store current page

    try {
      const existingPdfBytes = await pdfFile.arrayBuffer();
      const pdfDoc = await PDFDocument.load(existingPdfBytes);

      // Get font based on selection
      let font;
      try {
        font = await pdfDoc.embedFont(StandardFonts[fontFamily] || StandardFonts.Helvetica);
      } catch {
        font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      }

      const pages = pdfDoc.getPages();
      const page = pages[pageNumber - 1];
      const { height } = page.getSize();

      // Convert hex color to RGB
      const color = rgb(
        parseInt(textColor.hex.slice(1, 3), 16) / 255,
        parseInt(textColor.hex.slice(3, 5), 16) / 255,
        parseInt(textColor.hex.slice(5, 7), 16) / 255
      );

      // Calculate position (pdf-lib origin is bottom-left)
      const adjustedY = height - y;

      // Create text element for tracking
      const textElement = {
        id: Date.now() + Math.random(),
        text: textToAdd,
        x,
        y: adjustedY,
        pageNumber,
        fontSize,
        color: textColor.hex,
        fontFamily,
        originalText: textToAdd
      };

      page.drawText(textToAdd, {
        x,
        y: adjustedY,
        font,
        size: fontSize,
        color,
      });

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const newFile = new File([blob], fileName, { type: 'application/pdf' });

      // Add to history and update current file
      addToHistory(newFile);
      setPdfFile(newFile);

      // Add text element to tracking array
      setTextElements(prev => [...prev, textElement]);

      // Set text for editing if it was default text
      if (!text.trim()) {
        setText(textToAdd);
        setSelectedTextElement(textElement);
        setIsEditingText(true);
      }

      // Maintain current page after edit
      setTimeout(() => {
        setCurrentPage(currentPageBeforeEdit);
      }, 100);

      showNotification('Texto adicionado com sucesso! Clique no texto para editar.', 'success');
    } catch (error) {
      console.error('Error adding text to PDF:', error);
      showNotification('Erro ao adicionar texto ao PDF', 'error');
    }
  }, [pdfFile, text, textColor, fontSize, fontFamily, fileName, addToHistory, currentPage]);

  // Text Editing Functions
  const updateTextElement = useCallback(async (elementId, newText) => {
    if (!pdfFile || !newText.trim()) return;

    const currentPageBeforeEdit = currentPage;

    try {
      const existingPdfBytes = await pdfFile.arrayBuffer();
      const pdfDoc = await PDFDocument.load(existingPdfBytes);

      // Find the text element
      const element = textElements.find(el => el.id === elementId);
      if (!element) return;

      // Get font
      let font;
      try {
        font = await pdfDoc.embedFont(StandardFonts[element.fontFamily] || StandardFonts.Helvetica);
      } catch {
        font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      }

      const pages = pdfDoc.getPages();
      const page = pages[element.pageNumber - 1];

      // Convert hex color to RGB
      const color = rgb(
        parseInt(element.color.slice(1, 3), 16) / 255,
        parseInt(element.color.slice(3, 5), 16) / 255,
        parseInt(element.color.slice(5, 7), 16) / 255
      );

      // Remove old text by redrawing the page (simplified approach)
      // In a more complex implementation, you'd track text positions and overlay

      // Add updated text
      page.drawText(newText, {
        x: element.x,
        y: element.y,
        font,
        size: element.fontSize,
        color,
      });

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const newFile = new File([blob], fileName, { type: 'application/pdf' });

      // Update text elements array
      setTextElements(prev => prev.map(el =>
        el.id === elementId ? { ...el, text: newText } : el
      ));

      // Add to history and update current file
      addToHistory(newFile);
      setPdfFile(newFile);

      // Maintain current page
      setTimeout(() => {
        setCurrentPage(currentPageBeforeEdit);
      }, 100);

      showNotification('Texto atualizado com sucesso!', 'success');
      setIsEditingText(false);
      setSelectedTextElement(null);
    } catch (error) {
      console.error('Error updating text:', error);
      showNotification('Erro ao atualizar texto', 'error');
    }
  }, [pdfFile, textElements, fileName, addToHistory, currentPage]);

  // Page Interaction
  const handlePageClick = (event, pageNumber, x, y) => {
    if (selectedTool === 'text') {
      addTextToPdf(pageNumber, x, y);
    }
  };

  // File Operations
  const handleSave = async () => {
    if (!pdfFile) return;
    showNotification('PDF salvo localmente', 'success');
  };

  const handleDownload = async () => {
    if (!pdfFile) return;
    try {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(pdfFile);
      link.download = `edited-${fileName}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showNotification('PDF baixado com sucesso!', 'success');
    } catch (error) {
      showNotification('Erro ao baixar PDF', 'error');
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
        {/* Header */}
        <Header
          onFileUpload={onFileChange}
          onSave={handleSave}
          onDownload={handleDownload}
          hasFile={!!pdfFile}
          fileName={fileName}
          onUndo={handleUndo}
          onRedo={handleRedo}
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onFitToScreen={handleFitToScreen}
          canUndo={historyIndex > 0}
          canRedo={historyIndex < history.length - 1}
          zoom={zoom}
        />

        {/* Main Content */}
        <Box sx={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
          {/* Sidebar */}
          <Sidebar
            open={sidebarOpen}
            onClose={() => setSidebarOpen(false)}
            text={text}
            setText={setText}
            textColor={textColor}
            setTextColor={setTextColor}
            fontSize={fontSize}
            setFontSize={setFontSize}
            fontFamily={fontFamily}
            setFontFamily={setFontFamily}
            isBold={isBold}
            setIsBold={setIsBold}
            isItalic={isItalic}
            setIsItalic={setIsItalic}
            isUnderline={isUnderline}
            setIsUnderline={setIsUnderline}
            selectedTool={selectedTool}
            setSelectedTool={setSelectedTool}
            isEditingText={isEditingText}
            selectedTextElement={selectedTextElement}
            onUpdateText={updateTextElement}
            onCancelEdit={() => {
              setIsEditingText(false);
              setSelectedTextElement(null);
              setText('');
            }}
          />

          {/* PDF Viewer */}
          <PDFViewer
            pdfFile={pdfFile}
            onDocumentLoadSuccess={onDocumentLoadSuccess}
            onPageClick={handlePageClick}
            zoom={zoom}
            sidebarOpen={sidebarOpen}
            onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
            numPages={numPages}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </Box>

        {/* Notifications */}
        <Snackbar
          open={notification.open}
          autoHideDuration={4000}
          onClose={() => setNotification({ ...notification, open: false })}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setNotification({ ...notification, open: false })}
            severity={notification.severity}
            variant="filled"
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}

export default App;