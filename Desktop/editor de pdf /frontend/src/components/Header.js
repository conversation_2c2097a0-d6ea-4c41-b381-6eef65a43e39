import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Box,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  FitScreen as FitScreenIcon,
} from '@mui/icons-material';

const Header = ({
  onFileUpload,
  onSave,
  onDownload,
  hasFile,
  fileName,
  onUndo,
  onRedo,
  onZoomIn,
  onZoomOut,
  onFitToScreen,
  canUndo = false,
  canRedo = false,
  zoom = 100,
  onLoadTestPDF
}) => {

  const handleTestPDF = async () => {
    try {
      const response = await fetch(`${process.env.PUBLIC_URL || ''}/test.pdf`);
      const blob = await response.blob();
      const file = new File([blob], 'test.pdf', { type: 'application/pdf' });

      // Create a fake event object
      const fakeEvent = {
        target: {
          files: [file]
        }
      };

      onFileUpload(fakeEvent);
    } catch (error) {
      console.error('Error loading test PDF:', error);
    }
  };
  return (
    <AppBar position="static" elevation={0}>
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Logo e Título */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              fontWeight: 700,
              background: 'linear-gradient(45deg, #0284c7, #0ea5e9)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            PDF Editor Pro
          </Typography>
          {fileName && (
            <Chip 
              label={fileName} 
              size="small" 
              variant="outlined"
              sx={{ maxWidth: 200 }}
            />
          )}
        </Box>

        {/* Controles de Zoom */}
        {hasFile && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Diminuir zoom">
              <IconButton onClick={onZoomOut} size="small">
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Typography variant="body2" sx={{ minWidth: 50, textAlign: 'center' }}>
              {zoom}%
            </Typography>
            <Tooltip title="Aumentar zoom">
              <IconButton onClick={onZoomIn} size="small">
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Ajustar à tela">
              <IconButton onClick={onFitToScreen} size="small">
                <FitScreenIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        {/* Ações Principais */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Undo/Redo */}
          {hasFile && (
            <>
              <Tooltip title="Desfazer">
                <span>
                  <IconButton 
                    onClick={onUndo} 
                    disabled={!canUndo}
                    size="small"
                  >
                    <UndoIcon />
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip title="Refazer">
                <span>
                  <IconButton 
                    onClick={onRedo} 
                    disabled={!canRedo}
                    size="small"
                  >
                    <RedoIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </>
          )}

          {/* Upload */}
          <Button
            variant="outlined"
            startIcon={<CloudUploadIcon />}
            component="label"
            sx={{ ml: 1 }}
          >
            Abrir PDF
            <input
              type="file"
              hidden
              onChange={onFileUpload}
              accept="application/pdf"
            />
          </Button>

          {/* Test PDF Button */}
          <Button
            variant="text"
            onClick={handleTestPDF}
            size="small"
            sx={{ ml: 1 }}
          >
            Teste
          </Button>

          {/* Save/Download */}
          {hasFile && (
            <>
              <Tooltip title="Salvar alterações">
                <IconButton onClick={onSave} color="primary">
                  <SaveIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Baixar PDF">
                <IconButton onClick={onDownload} color="primary">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
