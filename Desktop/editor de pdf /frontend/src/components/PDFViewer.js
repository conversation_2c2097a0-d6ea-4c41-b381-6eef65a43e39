import React, { useState, useRef, useCallback } from 'react';
import { Document, Page } from 'react-pdf';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Fab,
  Tooltip,
  Alert,
  Stack,
} from '@mui/material';
import {
  Menu as MenuIcon,
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
} from '@mui/icons-material';

const PDFViewer = ({ 
  pdfFile, 
  onDocumentLoadSuccess, 
  onPageClick, 
  zoom = 100,
  sidebarOpen,
  onToggleSidebar,
  numPages,
  currentPage = 1,
  onPageChange
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const pdfContainerRef = useRef(null);

  const handleDocumentLoadSuccess = ({ numPages }) => {
    setLoading(false);
    setError(null);
    onDocumentLoadSuccess({ numPages });
  };

  const handleDocumentLoadError = (error) => {
    setLoading(false);
    console.error('PDF load error:', error);

    // Provide more specific error messages
    if (error.message && error.message.includes('worker')) {
      setError('Erro ao carregar o worker do PDF.js. Verifique sua conexão com a internet.');
    } else if (error.message && error.message.includes('Invalid PDF')) {
      setError('Arquivo PDF inválido ou corrompido.');
    } else {
      setError('Erro ao carregar o PDF. Verifique se o arquivo é válido.');
    }
  };

  const handlePageClick = useCallback((event, pageNumber) => {
    const pageElement = event.currentTarget;
    const rect = pageElement.getBoundingClientRect();

    // Calculate relative position within the page
    const x = (event.clientX - rect.left) / scale;
    const y = (event.clientY - rect.top) / scale;

    onPageClick(event, pageNumber, x, y);
  }, [onPageClick, scale]);

  const scale = zoom / 100;

  const EmptyState = () => (
    <Box 
      sx={{ 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '60vh',
        textAlign: 'center',
        color: 'text.secondary'
      }}
    >
      <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
        Nenhum PDF carregado
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Faça upload de um arquivo PDF para começar a editar
      </Typography>
      <Box
        sx={{
          width: 120,
          height: 160,
          border: '2px dashed',
          borderColor: 'divider',
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'action.hover',
        }}
      >
        <Typography variant="h6" color="text.secondary">
          PDF
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ 
      position: 'relative', 
      height: '100%',
      transition: 'margin-left 0.3s ease',
      marginLeft: sidebarOpen ? '320px' : 0,
    }}>
      {/* Toggle Sidebar Button */}
      <Fab
        size="small"
        color="primary"
        onClick={onToggleSidebar}
        sx={{
          position: 'fixed',
          top: 80,
          left: sidebarOpen ? 330 : 16,
          zIndex: 1200,
          transition: 'left 0.3s ease',
        }}
      >
        <MenuIcon />
      </Fab>

      {/* Main Content */}
      <Paper 
        elevation={0}
        sx={{ 
          height: 'calc(100vh - 64px)',
          overflow: 'auto',
          backgroundColor: 'grey.100',
          display: 'flex',
          flexDirection: 'column',
        }}
        ref={pdfContainerRef}
      >
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        {loading && (
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center',
            height: '50vh'
          }}>
            <CircularProgress />
          </Box>
        )}

        {!pdfFile && !loading && <EmptyState />}

        {pdfFile && (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            p: 2,
            gap: 2
          }}>
            {/* Page Navigation */}
            {numPages > 1 && (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 2,
                mb: 2,
                backgroundColor: 'background.paper',
                borderRadius: 2,
                p: 1,
                boxShadow: 1
              }}>
                <Tooltip title="Página anterior">
                  <span>
                    <Fab
                      size="small"
                      onClick={() => onPageChange(Math.max(1, currentPage - 1))}
                      disabled={currentPage <= 1}
                    >
                      <PrevIcon />
                    </Fab>
                  </span>
                </Tooltip>
                
                <Typography variant="body2" sx={{ minWidth: 100, textAlign: 'center' }}>
                  Página {currentPage} de {numPages}
                </Typography>
                
                <Tooltip title="Próxima página">
                  <span>
                    <Fab
                      size="small"
                      onClick={() => onPageChange(Math.min(numPages, currentPage + 1))}
                      disabled={currentPage >= numPages}
                    >
                      <NextIcon />
                    </Fab>
                  </span>
                </Tooltip>
              </Box>
            )}

            {/* PDF Document */}
            <Document 
              file={pdfFile} 
              onLoadSuccess={handleDocumentLoadSuccess}
              onLoadError={handleDocumentLoadError}
              loading={
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              }
            >
              <Stack spacing={2} alignItems="center">
                {/* Show only current page for better performance and page stability */}
                <Paper
                  key={`page_${currentPage}`}
                  elevation={3}
                  sx={{
                    p: 1,
                    cursor: 'crosshair',
                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                    '&:hover': {
                      transform: 'scale(1.02)',
                      boxShadow: 6,
                    },
                  }}
                  ref={pdfContainerRef}
                >
                  <Page
                    pageNumber={currentPage}
                    scale={scale}
                    onClick={(e) => handlePageClick(e, currentPage)}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    loading={
                      <Box sx={{
                        width: 600 * scale,
                        height: 800 * scale,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'grey.50'
                      }}>
                        <CircularProgress size={24} />
                      </Box>
                    }
                  />
                </Paper>
              </Stack>
            </Document>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default PDFViewer;
