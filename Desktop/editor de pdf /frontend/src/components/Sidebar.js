import React, { useState } from 'react';
import {
  Drawer,
  Box,
  Typography,
  Tabs,
  Tab,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  IconButton,
  Tooltip,
  Paper,
  Stack,
} from '@mui/material';
import {
  TextFields as TextIcon,
  Brush as BrushIcon,
  CropFree as SelectIcon,
  Close as CloseIcon,
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatUnderlined as UnderlineIcon,
} from '@mui/icons-material';
import { CirclePicker } from 'react-color';

const SIDEBAR_WIDTH = 320;

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`sidebar-tabpanel-${index}`}
    aria-labelledby={`sidebar-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
  </div>
);

const Sidebar = ({
  open,
  onClose,
  textColor,
  setTextColor,
  fontSize = 24,
  setFontSize,
  fontFamily = 'Helvetica',
  setFontFamily,
  isBold = false,
  setIsBold,
  isItalic = false,
  setIsItalic,
  isUnderline = false,
  setIsUnderline,
  selectedTool = 'text',
  setSelectedTool
}) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const fontFamilies = [
    'Helvetica',
    'Times-Roman',
    'Courier',
    'Arial',
    'Georgia',
    'Verdana',
  ];

  const tools = [
    { id: 'text', label: 'Texto', icon: <TextIcon /> },
    { id: 'draw', label: 'Desenhar', icon: <BrushIcon /> },
    { id: 'select', label: 'Selecionar', icon: <SelectIcon /> },
  ];

  return (
    <Drawer
      anchor="left"
      open={open}
      onClose={onClose}
      variant="persistent"
      sx={{
        width: SIDEBAR_WIDTH,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: SIDEBAR_WIDTH,
          boxSizing: 'border-box',
          borderRight: '1px solid',
          borderColor: 'divider',
          top: 64, // Height of AppBar
          height: 'calc(100vh - 64px)',
        },
      }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Header */}
        <Box sx={{ 
          p: 2, 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Typography variant="h6" fontWeight={600}>
            Ferramentas
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Tool Selection */}
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Ferramenta Ativa
          </Typography>
          <Stack direction="row" spacing={1}>
            {tools.map((tool) => (
              <Tooltip key={tool.id} title={tool.label}>
                <Paper
                  elevation={selectedTool === tool.id ? 2 : 0}
                  sx={{
                    p: 1,
                    cursor: 'pointer',
                    border: '1px solid',
                    borderColor: selectedTool === tool.id ? 'primary.main' : 'divider',
                    backgroundColor: selectedTool === tool.id ? 'primary.50' : 'transparent',
                    '&:hover': {
                      backgroundColor: 'action.hover',
                    },
                  }}
                  onClick={() => setSelectedTool(tool.id)}
                >
                  {tool.icon}
                </Paper>
              </Tooltip>
            ))}
          </Stack>
        </Box>

        {/* Tabs */}
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: '1px solid', borderColor: 'divider' }}
        >
          <Tab label="Texto" />
          <Tab label="Estilo" />
        </Tabs>

        {/* Tab Panels */}
        <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
          <TabPanel value={activeTab} index={0}>
            {/* Text Tab */}
            <Stack spacing={3}>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
                Ferramenta de Texto
              </Typography>

              <Typography variant="body2" color="text.secondary">
                • Clique no PDF para adicionar texto
                • Duplo clique no texto para editar
                • Arraste para reposicionar
              </Typography>

              <Box>
                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                  Tamanho da Fonte
                </Typography>
                <Slider
                  value={fontSize}
                  onChange={(e, value) => setFontSize(value)}
                  min={8}
                  max={72}
                  step={1}
                  valueLabelDisplay="auto"
                  marks={[
                    { value: 12, label: '12' },
                    { value: 24, label: '24' },
                    { value: 36, label: '36' },
                    { value: 48, label: '48' },
                  ]}
                />
              </Box>

              <FormControl fullWidth>
                <InputLabel>Fonte</InputLabel>
                <Select
                  value={fontFamily}
                  label="Fonte"
                  onChange={(e) => setFontFamily(e.target.value)}
                >
                  {fontFamilies.map((font) => (
                    <MenuItem key={font} value={font}>
                      {font}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Stack>
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            {/* Style Tab */}
            <Stack spacing={3}>
              <Box>
                <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                  Formatação
                </Typography>
                <Stack direction="row" spacing={1}>
                  <Tooltip title="Negrito">
                    <IconButton
                      onClick={() => setIsBold(!isBold)}
                      color={isBold ? 'primary' : 'default'}
                      sx={{
                        border: '1px solid',
                        borderColor: isBold ? 'primary.main' : 'divider',
                      }}
                    >
                      <BoldIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Itálico">
                    <IconButton
                      onClick={() => setIsItalic(!isItalic)}
                      color={isItalic ? 'primary' : 'default'}
                      sx={{
                        border: '1px solid',
                        borderColor: isItalic ? 'primary.main' : 'divider',
                      }}
                    >
                      <ItalicIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Sublinhado">
                    <IconButton
                      onClick={() => setIsUnderline(!isUnderline)}
                      color={isUnderline ? 'primary' : 'default'}
                      sx={{
                        border: '1px solid',
                        borderColor: isUnderline ? 'primary.main' : 'divider',
                      }}
                    >
                      <UnderlineIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Box>

              <Divider />

              <Box>
                <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                  Cor do Texto
                </Typography>
                <CirclePicker 
                  color={textColor} 
                  onChangeComplete={setTextColor}
                  width="100%"
                />
              </Box>
            </Stack>
          </TabPanel>
        </Box>


      </Box>
    </Drawer>
  );
};

export default Sidebar;
