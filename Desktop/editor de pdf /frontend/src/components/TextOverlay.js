import React, { useState, useRef, useEffect } from 'react';
import { Box, TextField, ClickAwayListener } from '@mui/material';

const TextOverlay = ({
  textElements,
  onUpdateText,
  onDeleteText,
  onMoveText,
  onStartEdit,
  scale = 1,
  pageNumber = 1
}) => {
  const [draggedElement, setDraggedElement] = useState(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const handleSingleClick = (element) => {
    if (element.isVirtual && !element.isEditing) {
      // Start editing virtual text
      onStartEdit(element.id);
    }
  };

  const handleDoubleClick = (element) => {
    if (!element.isVirtual) {
      // Start editing confirmed text
      onStartEdit(element.id);
    }
  };

  const handleSaveEdit = (element, newText) => {
    if (newText.trim()) {
      onUpdateText(element.id, newText.trim());
    } else if (element.isVirtual) {
      // Delete empty virtual text
      onDeleteText(element.id);
    }
  };

  const handleCancelEdit = (element) => {
    if (element.isVirtual) {
      // Cancel virtual text
      onDeleteText(element.id);
    } else {
      // Just stop editing confirmed text
      onStartEdit(null);
    }
  };

  const handleMouseDown = (e, element) => {
    if (element.isEditing) return; // Don't drag while editing

    e.preventDefault();
    setDraggedElement(element);

    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (!draggedElement) return;
    
    e.preventDefault();
    const container = e.currentTarget.closest('.pdf-page-container');
    if (!container) return;
    
    const rect = container.getBoundingClientRect();
    const newX = (e.clientX - rect.left - dragOffset.x) / scale;
    const newY = (e.clientY - rect.top - dragOffset.y) / scale;
    
    onMoveText(draggedElement.id, newX, newY);
  };

  const handleMouseUp = () => {
    setDraggedElement(null);
    setDragOffset({ x: 0, y: 0 });
  };

  useEffect(() => {
    if (draggedElement) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedElement, dragOffset, scale]);

  // Filter text elements for current page
  const pageTextElements = textElements.filter(el => el.pageNumber === pageNumber);

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
      className="text-overlay"
    >
      {pageTextElements.map((element) => (
        <Box
          key={element.id}
          sx={{
            position: 'absolute',
            left: element.x * scale,
            top: element.y * scale,
            pointerEvents: 'auto',
            cursor: element.isEditing ? 'text' : 'move',
            userSelect: 'none',
            border: element.isVirtual ? '1px dashed #007bff' : 'none',
            backgroundColor: element.isVirtual ? 'rgba(0, 123, 255, 0.1)' : 'transparent',
            '&:hover': {
              backgroundColor: element.isVirtual ? 'rgba(0, 123, 255, 0.2)' : 'rgba(0, 123, 255, 0.1)',
              borderRadius: '2px'
            }
          }}
          onClick={() => handleSingleClick(element)}
          onDoubleClick={() => handleDoubleClick(element)}
          onMouseDown={(e) => !element.isEditing && handleMouseDown(e, element)}
        >
          {element.isEditing ? (
            <ClickAwayListener onClickAway={() => handleSaveEdit(element, element.text)}>
              <TextField
                defaultValue={element.text}
                onChange={(e) => {
                  // Update element text in real time for virtual elements
                  if (element.isVirtual && onMoveText) {
                    // This is a hack to update text - we'll use onMoveText to trigger updates
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSaveEdit(element, e.target.value);
                  } else if (e.key === 'Escape') {
                    handleCancelEdit(element);
                  }
                }}
                autoFocus
                variant="outlined"
                size="small"
                placeholder={element.isVirtual ? "Digite o texto..." : "Editar texto"}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: element.fontSize * scale,
                    fontFamily: element.fontFamily,
                    color: element.color,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    minWidth: '120px',
                    border: element.isVirtual ? '2px solid #007bff' : '1px solid #ccc'
                  }
                }}
              />
            </ClickAwayListener>
          ) : (
            <Box
              sx={{
                fontSize: element.fontSize * scale,
                fontFamily: element.fontFamily,
                color: element.color,
                fontWeight: element.isBold ? 'bold' : 'normal',
                fontStyle: element.isItalic || element.isVirtual ? 'italic' : 'normal',
                textDecoration: element.isUnderline ? 'underline' : 'none',
                padding: '4px 6px',
                borderRadius: '2px',
                transition: 'all 0.2s ease',
                minHeight: element.fontSize * scale,
                display: 'flex',
                alignItems: 'center',
                opacity: element.isVirtual ? 0.8 : 1
              }}
            >
              {element.text}
              {element.isVirtual && (
                <Box component="span" sx={{
                  ml: 1,
                  fontSize: '0.7em',
                  color: '#007bff',
                  fontWeight: 'normal'
                }}>
                  (clique para editar)
                </Box>
              )}
            </Box>
          )}
        </Box>
      ))}
    </Box>
  );
};

export default TextOverlay;
