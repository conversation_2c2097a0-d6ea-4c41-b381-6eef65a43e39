import React, { useState, useRef, useEffect } from 'react';
import { Box, TextField, ClickAwayListener } from '@mui/material';

const TextOverlay = ({ 
  textElements, 
  onUpdateText, 
  onDeleteText, 
  onMoveText,
  scale = 1,
  pageNumber = 1 
}) => {
  const [editingId, setEditingId] = useState(null);
  const [editText, setEditText] = useState('');
  const [draggedElement, setDraggedElement] = useState(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const handleDoubleClick = (element) => {
    setEditingId(element.id);
    setEditText(element.text);
  };

  const handleSaveEdit = () => {
    if (editingId && editText.trim()) {
      onUpdateText(editingId, editText.trim());
    }
    setEditingId(null);
    setEditText('');
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditText('');
  };

  const handleMouseDown = (e, element) => {
    if (editingId) return; // Don't drag while editing
    
    e.preventDefault();
    setDraggedElement(element);
    
    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (!draggedElement) return;
    
    e.preventDefault();
    const container = e.currentTarget.closest('.pdf-page-container');
    if (!container) return;
    
    const rect = container.getBoundingClientRect();
    const newX = (e.clientX - rect.left - dragOffset.x) / scale;
    const newY = (e.clientY - rect.top - dragOffset.y) / scale;
    
    onMoveText(draggedElement.id, newX, newY);
  };

  const handleMouseUp = () => {
    setDraggedElement(null);
    setDragOffset({ x: 0, y: 0 });
  };

  useEffect(() => {
    if (draggedElement) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedElement, dragOffset, scale]);

  // Filter text elements for current page
  const pageTextElements = textElements.filter(el => el.pageNumber === pageNumber);

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
      className="text-overlay"
    >
      {pageTextElements.map((element) => (
        <Box
          key={element.id}
          sx={{
            position: 'absolute',
            left: element.x * scale,
            top: element.y * scale,
            pointerEvents: 'auto',
            cursor: editingId === element.id ? 'text' : 'move',
            userSelect: 'none',
            '&:hover': {
              backgroundColor: 'rgba(0, 123, 255, 0.1)',
              borderRadius: '2px'
            }
          }}
          onDoubleClick={() => handleDoubleClick(element)}
          onMouseDown={(e) => handleMouseDown(e, element)}
        >
          {editingId === element.id ? (
            <ClickAwayListener onClickAway={handleSaveEdit}>
              <TextField
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSaveEdit();
                  } else if (e.key === 'Escape') {
                    handleCancelEdit();
                  }
                }}
                autoFocus
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: element.fontSize * scale,
                    fontFamily: element.fontFamily,
                    color: element.color,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    minWidth: '100px'
                  }
                }}
              />
            </ClickAwayListener>
          ) : (
            <Box
              sx={{
                fontSize: element.fontSize * scale,
                fontFamily: element.fontFamily,
                color: element.color,
                fontWeight: element.isBold ? 'bold' : 'normal',
                fontStyle: element.isItalic ? 'italic' : 'normal',
                textDecoration: element.isUnderline ? 'underline' : 'none',
                padding: '2px 4px',
                borderRadius: '2px',
                transition: 'background-color 0.2s ease',
                minHeight: element.fontSize * scale,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              {element.text}
            </Box>
          )}
        </Box>
      ))}
    </Box>
  );
};

export default TextOverlay;
